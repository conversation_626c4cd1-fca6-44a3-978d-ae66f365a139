import ReactApexChart from "react-apexcharts";

// Helper to map status code to category
function getStatusCategory(statusCode) {
  if (statusCode === 200) return "success";
  if (statusCode >= 400 && statusCode < 500) {
    switch (statusCode) {
      case 400:
        return "bad_request";
      case 401:
        return "unauthorized";
      case 403:
        return "forbidden";
      case 404:
        return "not_found";
      case 405:
        return "method_not_allowed";
      case 409:
        return "conflict";
      case 422:
        return "validation_error";
      case 429:
        return "rate_limited";
      default:
        return "client_error";
    }
  }
  if (statusCode >= 500) {
    switch (statusCode) {
      case 500:
        return "internal_error";
      case 502:
        return "bad_gateway";
      case 503:
        return "service_unavailable";
      case 504:
        return "gateway_timeout";
      default:
        return "server_error";
    }
  }
  return "other";
}

// Color mapping for each category
const statusColors = {
  success: "#22c55e",
  bad_request: "#f59e42",
  unauthorized: "#f542a4",
  forbidden: "#ef4444",
  not_found: "#ef4444", // đỏ cho 404
  method_not_allowed: "#fbbf24",
  conflict: "#eac24aff",
  validation_error: "#38bdf8",
  rate_limited: "#6366f1",
  client_error: "#f97316",
  internal_error: "#ef4444",
  bad_gateway: "#f43f5e",
  service_unavailable: "#a3a3a3",
  gateway_timeout: "#f59e42",
  server_error: "#f87171",
  other: "#a3a3a3",
};

const StatusCode = ({ statusCodeData = [] }) => {
  // Chuyển dữ liệu statusCodeData sang format cho ApexChart
  const series = statusCodeData.map((item) => item.count);
  const labels = statusCodeData.map((item) =>
    getStatusCategory(item.status_code)
  );
  const colors = statusCodeData.map(
    (item) => statusColors[getStatusCategory(item.status_code)] || "#a3a3a3"
  );

  const options = {
    chart: {
      type: "pie",
    },
    labels,
    legend: {
      show: true,
      position: "right",
      fontSize: "16px",
      fontWeight: 500,
      markers: {
        width: 12,
        height: 12,
        radius: 6,
      },
      itemMargin: {
        vertical: 8,
        horizontal: 0,
      },
    },
    colors: colors,
    dataLabels: {
      enabled: true,
      formatter: function (val, opts) {
        // Chỉ hiển thị số count trên pie
        return opts.w.globals.series[opts.seriesIndex];
      },
    },
    tooltip: {
      y: {
        formatter: function (val, opts) {
          // Khi hover mới hiện category
          return labels[opts.seriesIndex] + ": " + val;
        },
      },
    },
  };

  return (
    <div className="col-12 col-md-6 col-lg-4 mt-3">
      <div className="card h-100 radius-8 border-0">
        <div className="card-header">
          <div className="d-flex align-items-center flex-wrap gap-2 justify-content-between">
            <h6 className="mb-2 fw-bold text-lg mb-0">Mã trạng thái</h6>
          </div>
        </div>
        <div className="card-body">
          <div className="apexcharts-tooltip-z-none d-flex justify-content-center">
            <ReactApexChart
              options={options}
              series={series}
              type="pie"
              height={254}
              width={420}
            />
          </div>
        </div>
      </div>
    </div>
  );
};
export default StatusCode;
