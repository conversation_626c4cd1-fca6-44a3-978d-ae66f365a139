import React, { useState, useEffect, useCallback } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import TimeRangeFilter from "../components/dashboard/TimeRangeFilter";
import StatisticsCards from "../components/dashboard/StatisticsCards";
import ApiCallsChart from "../components/dashboard/ApiCallsChart";
import StatusCode from "../components/dashboard/StatusCode.jsx";
import ApiCallHistoryTable from "../components/dashboard/ApiCallHistoryTable";
import { useAnalytics } from "../hooks/useAnalytics";

const DashBoard = () => {
  const [selectedTimeRange, setSelectedTimeRange] = useState("today");
  const [isLoading, setIsLoading] = useState(true);
  const [callHistoryData, setCallHistoryData] = useState([]);

  // API hooks - chỉ sử dụng Analytics APIs
  const { getCallHistory, loading: analyticsLoading } = useAnalytics();

  // Data arrays - mở rộng time range options
  const timeRangeOptions = [
    { id: "last15minutes", label: "15 phút qua" },
    { id: "last30minutes", label: "30 phút qua" },
    { id: "last1hour", label: "1 tiếng qua" },
    { id: "last12hours", label: "12 tiếng qua" },
    { id: "today", label: "Hôm nay" },
    { id: "last7days", label: "7 ngày qua" },
    { id: "last14days", label: "14 ngày qua" },
    { id: "last30days", label: "30 ngày qua" },
  ];

  // API params cho các time range
  const getApiParams = (selectedRange) => {
    switch (selectedRange) {
      case "last15minutes":
      case "last30minutes":
      case "last1hour":
        return { timeRange: 1, timeUnit: "hours", groupBy: "minute" };
      case "last12hours":
        return { timeRange: 12, timeUnit: "hours", groupBy: "hour" };
      case "today":
        return { timeRange: 1, timeUnit: "days", groupBy: "hour" };
      case "last7days":
        return { timeRange: 7, timeUnit: "days", groupBy: "day" };
      case "last14days":
        return { timeRange: 14, timeUnit: "days", groupBy: "day" };
      case "last30days":
        return { timeRange: 30, timeUnit: "days", groupBy: "day" };
      default:
        return { timeRange: 1, timeUnit: "days", groupBy: "hour" };
    }
  };

  // Load call history data duy nhất
  const loadAnalyticsData = useCallback(async () => {
    setIsLoading(true);
    const currentToken = localStorage.getItem("authToken");
    if (!currentToken) {
      setIsLoading(false);
      return;
    }
    try {
      // Lấy toàn bộ lịch sử không filter, để FE tự filter
      const historyResult = await getCallHistory();
      if (historyResult.success) {
        setCallHistoryData(historyResult.data);
        console.log(`Loaded ${historyResult.data.length} call history records`);
      } else {
        setCallHistoryData([]);
      }
    } catch (error) {
      setCallHistoryData([]);
    } finally {
      setIsLoading(false);
    }
  }, [getCallHistory]);

  // Load data on component mount and when time range changes
  useEffect(() => {
    loadAnalyticsData();
  }, [loadAnalyticsData]);

  // Filter dữ liệu theo selectedTimeRange ở FE
  const getFilteredData = (data) => {
    if (!data || data.length === 0) return [];

    const now = new Date();
    let cutoffDate;

    switch (selectedTimeRange) {
      case "last15minutes":
        cutoffDate = new Date(now.getTime() - 15 * 60 * 1000);
        break;
      case "last30minutes":
        cutoffDate = new Date(now.getTime() - 30 * 60 * 1000);
        break;
      case "last1hour":
        cutoffDate = new Date(now.getTime() - 60 * 60 * 1000);
        break;
      case "last12hours":
        cutoffDate = new Date(now.getTime() - 12 * 60 * 60 * 1000);
        break;
      case "today":
        cutoffDate = new Date(now.getFullYear(), now.getMonth(), now.getDate());
        break;
      case "last7days":
        cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case "last14days":
        cutoffDate = new Date(now.getTime() - 14 * 24 * 60 * 60 * 1000);
        break;
      case "last30days":
        cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
        break;
      default:
        return data; // Trả về toàn bộ nếu không filter
    }

    return data.filter((item) => {
      const itemDate = new Date(item.date);
      return itemDate >= cutoffDate;
    });
  };

  // Helper: Tính toán statistics cards
  const getStatisticsFromHistory = (data) => {
    const totalCalls = data.length;
    const totalErrors = data.filter((item) => item.status_code >= 400).length;
    const totalSuccess = data.filter(
      (item) => item.status_code >= 200 && item.status_code < 300
    ).length;
    const avgResponseTime =
      totalCalls > 0
        ? Math.round(
            data.reduce(
              (sum, item) => sum + (parseInt(item.response_time) || 0),
              0
            ) / totalCalls
          )
        : 0;
    return [
      {
        title: "Total calls",
        value: totalCalls.toString(),
        subtitle: "API calls",
      },
      {
        title: "Total success",
        value: totalSuccess.toString(),
        subtitle: "success",
      },
      {
        title: "Total errors",
        value: totalErrors.toString(),
        subtitle: "errors",
      },
      {
        title: "Average response time",
        value: avgResponseTime.toString(),
        subtitle: "milliseconds",
      },
    ];
  };

  // Helper: Chuẩn hóa dữ liệu cho line chart (group by time)
  const getCallsOverTimeData = (data, groupBy) => {
    if (!data || data.length === 0) return [];

    // Với các time range ngắn hạn (dưới 1 ngày), hiển thị từng request riêng biệt
    const shortTermRanges = [
      "last15minutes",
      "last30minutes",
      "last1hour",
      "last12hours",
    ];

    if (shortTermRanges.includes(selectedTimeRange)) {
      // Trả về từng request riêng biệt, không gộp
      return data
        .map((item) => ({
          time: item.date, // Giữ nguyên thời gian thực của request
          api_calls: 1, // Mỗi request = 1 call
          avg_response_time: parseInt(item.response_time) || 0,
          // Thêm thông tin chi tiết cho tooltip
          method: item.method,
          endpoint: item.endpoint,
          status_code: item.status_code,
        }))
        .sort((a, b) => new Date(a.time) - new Date(b.time));
    }

    // Với time range dài hạn, vẫn gộp như cũ
    const groupMap = {};

    // Tạo time slots cho "Hôm nay"
    if (selectedTimeRange === "today") {
      const today = new Date();
      const startOfDay = new Date(
        today.getFullYear(),
        today.getMonth(),
        today.getDate()
      );

      for (let hour = 0; hour < 24; hour++) {
        const hourDate = new Date(startOfDay);
        hourDate.setHours(hour);
        const key = hourDate.toISOString().slice(0, 13) + ":00:00.000Z";
        groupMap[key] = { count: 0, totalResponse: 0 };
      }
    }

    // Fill data vào các slot cho time range dài hạn
    data.forEach((item) => {
      const date = new Date(item.date);
      let key;

      if (selectedTimeRange === "today") {
        key = date.toISOString().slice(0, 13) + ":00:00.000Z";
      } else {
        key = date.toISOString().slice(0, 10) + "T00:00:00.000Z";
        if (!groupMap[key]) groupMap[key] = { count: 0, totalResponse: 0 };
      }

      if (groupMap[key]) {
        groupMap[key].count++;
        groupMap[key].totalResponse += parseInt(item.response_time) || 0;
      }
    });

    // Trả về mảng [{time, api_calls, avg_response_time}]
    return Object.entries(groupMap)
      .map(([time, val]) => ({
        time,
        api_calls: val.count,
        avg_response_time:
          val.count > 0 ? Math.round(val.totalResponse / val.count) : 0,
      }))
      .sort((a, b) => a.time.localeCompare(b.time));
  };

  // Helper: Chuẩn hóa dữ liệu cho pie chart (group by status_code)
  const getStatusCodeData = (data) => {
    if (!data || data.length === 0) return [];
    const codeMap = {};
    data.forEach((item) => {
      const code = item.status_code;
      if (!codeMap[code]) codeMap[code] = 0;
      codeMap[code]++;
    });
    return Object.entries(codeMap).map(([status_code, count]) => ({
      status_code: Number(status_code),
      count,
    }));
  };

  const filteredData = getFilteredData(callHistoryData);

  return (
    <>
      <MasterLayout>
        <section className="row">
          <Breadcrumb title="Tổng quan" />
          <TimeRangeFilter
            timeRangeOptions={timeRangeOptions}
            selectedTimeRange={selectedTimeRange}
            setSelectedTimeRange={setSelectedTimeRange}
          />
          <StatisticsCards
            statisticsCards={getStatisticsFromHistory(filteredData)}
            callsData={getCallsOverTimeData(
              filteredData,
              selectedTimeRange === "today" ? "hour" : "day"
            )}
            rawData={filteredData}
            timeRange={selectedTimeRange}
          />
        </section>

        {isLoading ? (
          <section className="row">
            <div className="col-12 text-center py-5">
              <div className="spinner-border text-primary" role="status">
                <span className="visually-hidden">Loading...</span>
              </div>
              <p className="mt-3">Đang tải dữ liệu dashboard...</p>
            </div>
          </section>
        ) : (
          <>
            <section>
              <div className="row">
                <ApiCallsChart
                  callsData={getCallsOverTimeData(
                    filteredData,
                    selectedTimeRange === "today" ? "hour" : "day"
                  )}
                  timeRange={selectedTimeRange}
                  groupBy={selectedTimeRange === "today" ? "hour" : "day"}
                />
                <StatusCode statusCodeData={getStatusCodeData(filteredData)} />
              </div>
            </section>

            {/* API Call History Table */}
            <section className="row mt-3">
              <div className="col-12">
                <ApiCallHistoryTable callHistoryData={filteredData} />
              </div>
            </section>
          </>
        )}
      </MasterLayout>
    </>
  );
};

export default DashBoard;
