import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const usePlans = () => {
  const { apiCall, loading, error } = useApi();
  const [plans, setPlans] = useState([]);
  const [currentPlan, setCurrentPlan] = useState(null);

  // Get available plans
  const getAvailablePlans = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.AVAILABLE, {
        method: 'GET',
      });

      console.log('usePlans API response:', response); // Debug log

      if (response.success && response.plans) {
        setPlans(response.plans);
        return {
          success: true,
          data: response.plans, // Map plans to data để component dùng được
          message: response.message || 'Available plans loaded successfully'
        };
      } else if (response.success && response.data) {
        // Fallback cho trường hợp API trả về data
        setPlans(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Available plans loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load available plans');
      }
    } catch (err) {
      console.error('usePlans error:', err);
      return {
        success: false,
        message: err.message || 'Failed to load available plans'
      };
    }
  }, [apiCall]);

  // Get plan usage statistics
  const getPlanUsage = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.USAGE, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Plan usage loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load plan usage');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load plan usage'
      };
    }
  }, [apiCall]);

  // Compare plans
  const comparePlans = useCallback(async (planIds) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.COMPARE, {
        method: 'POST',
        data: { planIds },
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Plans comparison loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to compare plans');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to compare plans'
      };
    }
  }, [apiCall]);

  // Subscribe to a plan
  const subscribeToPlan = useCallback(async (planId, paymentData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.SUBSCRIBE, {
        method: 'POST',
        data: { planId, ...paymentData },
      });

      if (response.success && response.data) {
        setCurrentPlan(response.data.plan);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Successfully subscribed to plan'
        };
      } else {
        throw new Error(response.message || 'Failed to subscribe to plan');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to subscribe to plan'
      };
    }
  }, [apiCall]);

  // Upgrade plan
  const upgradePlan = useCallback(async (newPlanId, paymentData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.UPGRADE, {
        method: 'POST',
        data: { newPlanId, ...paymentData },
      });

      if (response.success && response.data) {
        setCurrentPlan(response.data.plan);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Plan upgraded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to upgrade plan');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to upgrade plan'
      };
    }
  }, [apiCall]);

  // Get subscription status
  const getSubscriptionStatus = useCallback(async () => {
    try {
      const response = await apiCall(API_ENDPOINTS.PLANS.SUBSCRIPTION_STATUS, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setCurrentPlan(response.data.currentPlan);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Subscription status loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load subscription status');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load subscription status'
      };
    }
  }, [apiCall]);

  return {
    plans,
    currentPlan,
    getAvailablePlans,
    getPlanUsage,
    comparePlans,
    subscribeToPlan,
    upgradePlan,
    getSubscriptionStatus,
    loading,
    error
  };
};

export default usePlans;
