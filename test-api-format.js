// Test API format conversion
const convertTimeRangeToNumeric = (selectedRange) => {
    const rangeMap = {
        "last15minutes": { days: 1, hours: 0.25 }, // 15 phút = 0.25 giờ, gửi 1 ngày để API có data
        "last30minutes": { days: 1, hours: 0.5 },  // 30 phút = 0.5 giờ
        "last1hour": { days: 1, hours: 1 },
        "last4hours": { days: 1, hours: 4 },
        "last12hours": { days: 1, hours: 12 },
        "last24hours": { days: 1, hours: 24 },
        "last7days": { days: 7, hours: 0 },
        "last30days": { days: 30, hours: 0 }
    };

    const config = rangeMap[selectedRange];
    if (!config) return 1; // default

    // Cho các range ngắn hơn 1 ngày, vẫn gửi timeRange=1 để có data
    // API sẽ filter theo groupBy (minute/hour)
    return config.days;
};

// Test cases
const testCases = [
    "last15minutes",
    "last30minutes",
    "last1hour",
    "last4hours",
    "last12hours",
    "last24hours",
    "last7days",
    "last30days"
];

console.log("Testing API format conversion:");
testCases.forEach(range => {
    const numeric = convertTimeRangeToNumeric(range);
    console.log(`${range} -> timeRange: ${numeric}`);
});

// Test groupBy logic
const getGroupBy = (selectedRange) => {
    switch (selectedRange) {
        case "last15minutes":
        case "last30minutes":
        case "last1hour":
            return "minute";
        case "last4hours":
        case "last12hours":
        case "last24hours":
            return "hour";
        case "last7days":
        case "last30days":
            return "day";
        default:
            return "hour";
    }
};

console.log("\nTesting groupBy logic:");
testCases.forEach(range => {
    const groupBy = getGroupBy(range);
    console.log(`${range} -> groupBy: ${groupBy}`);
});

console.log("\nAPI URL examples:");
testCases.forEach(range => {
    const timeRange = convertTimeRangeToNumeric(range);
    const groupBy = getGroupBy(range);
    console.log(`${range} -> /analytics/calls-over-time?timeRange=${timeRange}&groupBy=${groupBy}`);
});
