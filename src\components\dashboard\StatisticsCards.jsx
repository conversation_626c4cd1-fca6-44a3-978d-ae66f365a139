import React from "react";
import { Icon } from "@iconify/react/dist/iconify.js";
import ReactApexChart from "react-apexcharts";

const StatisticsCards = ({
  statisticsCards,
  callsData = [],
  rawData = [],
  timeRange = "today",
}) => {
  // Tạo mini chart với dữ liệu thực
  const createMiniChart = (chartColor, title, data = []) => {
    // Sử dụng rawData thay vì callsData để có dữ liệu chính xác theo filter
    const actualData = rawData.length > 0 ? rawData : data;

    // Tạo dữ liệu phù hợp cho từng loại card
    let chartData = [];

    if (actualData.length > 0) {
      // Với time range ngắn hạn, hiển thị trend của từng request
      const shortTermRanges = [
        "last15minutes",
        "last30minutes",
        "last1hour",
        "last12hours",
      ];
      const isDetailedView = shortTermRanges.includes(timeRange);

      if (isDetailedView) {
        // Với detailed view, tạo time-based trend từ raw data
        const sortedData = actualData
          .sort((a, b) => new Date(a.date) - new Date(b.date))
          .slice(-9); // Lấy 9 request gần nhất

        switch (title.toLowerCase()) {
          case "total calls":
            chartData = sortedData.map(() => 1); // Mỗi request = 1 call
            break;
          case "total success":
            chartData = sortedData.map((item) =>
              item.status_code >= 200 && item.status_code < 300 ? 1 : 0
            );
            break;
          case "total errors":
            chartData = sortedData.map((item) =>
              item.status_code >= 400 ? 1 : 0
            );
            break;
          case "average response time":
            chartData = sortedData.map(
              (item) => parseInt(item.response_time) || 0
            );
            break;
          default:
            chartData = sortedData.map(() => 1);
        }
      } else if (timeRange === "today") {
        // Với filter "Hôm nay", hiển thị đầy đủ 24 giờ (00:00 - 23:00)
        const hourGroups = {};

        // Khởi tạo tất cả 24 giờ với giá trị 0
        for (let hour = 0; hour < 24; hour++) {
          hourGroups[hour] = {
            total: 0,
            success: 0,
            errors: 0,
            responseSum: 0,
          };
        }

        // Fill data vào các slot giờ
        actualData.forEach((item) => {
          const hour = new Date(item.date).getHours();
          hourGroups[hour].total++;
          if (item.status_code >= 200 && item.status_code < 300) {
            hourGroups[hour].success++;
          }
          if (item.status_code >= 400) {
            hourGroups[hour].errors++;
          }
          hourGroups[hour].responseSum += parseInt(item.response_time) || 0;
        });

        // Tạo array cho tất cả 24 giờ
        const allHours = Array.from({ length: 24 }, (_, i) => i);

        switch (title.toLowerCase()) {
          case "total calls":
            chartData = allHours.map((hour) => hourGroups[hour].total);
            break;
          case "total success":
            chartData = allHours.map((hour) => hourGroups[hour].success);
            break;
          case "total errors":
            chartData = allHours.map((hour) => hourGroups[hour].errors);
            break;
          case "average response time":
            chartData = allHours.map((hour) =>
              hourGroups[hour].total > 0
                ? Math.round(
                    hourGroups[hour].responseSum / hourGroups[hour].total
                  )
                : 0
            );
            break;
          default:
            chartData = allHours.map((hour) => hourGroups[hour].total);
        }
      } else {
        // Với time range dài hạn, group theo time như cũ
        switch (title.toLowerCase()) {
          case "total calls":
            // Group theo giờ/ngày và đếm
            const timeGroups = {};
            actualData.forEach((item) => {
              const timeKey = new Date(item.date).toISOString().slice(0, 13); // Group theo giờ
              timeGroups[timeKey] = (timeGroups[timeKey] || 0) + 1;
            });
            chartData = Object.values(timeGroups).slice(-9);
            break;
          case "total success":
            const successGroups = {};
            actualData.forEach((item) => {
              const timeKey = new Date(item.date).toISOString().slice(0, 13);
              if (!successGroups[timeKey]) successGroups[timeKey] = 0;
              if (item.status_code >= 200 && item.status_code < 300) {
                successGroups[timeKey]++;
              }
            });
            chartData = Object.values(successGroups).slice(-9);
            break;
          case "total errors":
            const errorGroups = {};
            actualData.forEach((item) => {
              const timeKey = new Date(item.date).toISOString().slice(0, 13);
              if (!errorGroups[timeKey]) errorGroups[timeKey] = 0;
              if (item.status_code >= 400) {
                errorGroups[timeKey]++;
              }
            });
            chartData = Object.values(errorGroups).slice(-9);
            break;
          case "average response time":
            const responseGroups = {};
            actualData.forEach((item) => {
              const timeKey = new Date(item.date).toISOString().slice(0, 13);
              if (!responseGroups[timeKey])
                responseGroups[timeKey] = { total: 0, sum: 0 };
              responseGroups[timeKey].total++;
              responseGroups[timeKey].sum += parseInt(item.response_time) || 0;
            });
            chartData = Object.values(responseGroups)
              .map((g) => (g.total > 0 ? Math.round(g.sum / g.total) : 0))
              .slice(-9);
            break;
          default:
            chartData = [0, 0, 0, 0, 0, 0, 0, 0, 0];
        }
      }
    } else {
      // Nếu không có data, luôn truyền [0] cho chart để chỉ vẽ một điểm phẳng
      chartData = [0];
    }

    // Nếu tất cả giá trị đều là 0, chỉ truyền [0] cho chart để không bị zigzag
    let safeChartData = chartData;
    if (
      Array.isArray(chartData) &&
      chartData.length > 0 &&
      chartData.every((v) => v === 0)
    ) {
      safeChartData = [0];
    }

    const series = [
      {
        name: "series1",
        data: safeChartData,
      },
    ];

    const options = {
      chart: {
        type: "area",
        width: 80,
        height: 42,
        sparkline: {
          enabled: true,
        },
        toolbar: {
          show: false,
        },
        padding: {
          left: 0,
          right: 0,
          top: 0,
          bottom: 0,
        },
      },
      dataLabels: {
        enabled: false,
      },
      stroke: {
        curve: "smooth",
        width: 2,
        colors: [chartColor],
        lineCap: "round",
      },
      grid: {
        show: true,
        borderColor: "transparent",
        strokeDashArray: 0,
        position: "back",
        padding: {
          top: -3,
          right: 0,
          bottom: 0,
          left: 0,
        },
      },
      fill: {
        type: "gradient",
        colors: [chartColor],
        gradient: {
          shade: "light",
          type: "vertical",
          shadeIntensity: 0.5,
          gradientToColors: [`${chartColor}00`],
          inverseColors: false,
          opacityFrom: 0.75,
          opacityTo: 0.3,
          stops: [0, 100],
        },
      },
      markers: {
        colors: [chartColor],
        strokeWidth: 2,
        size: 0,
        hover: {
          size: 8,
        },
      },
      xaxis: {
        labels: {
          show: false,
        },
        tooltip: {
          enabled: false,
        },
      },
      yaxis: {
        labels: {
          show: false,
        },
      },
      tooltip: {
        enabled: false,
      },
    };

    return (
      <ReactApexChart
        options={options}
        series={series}
        type="area"
        height={42}
        width={80}
      />
    );
  };
  // Mapping icons and colors cho từng loại card
  const getCardConfig = (title) => {
    switch (title.toLowerCase()) {
      case "total calls":
        return {
          icon: "mdi:api",
          bgColor: "bg-primary-600",
          gradientClass: "bg-gradient-end-1",
          chartColor: "#487fff",
        };
      case "total success":
        return {
          icon: "mdi:check-circle",
          bgColor: "bg-success-main",
          gradientClass: "bg-gradient-end-2",
          chartColor: "#45b369",
        };
      case "total errors":
        return {
          icon: "mdi:alert-circle",
          bgColor: "bg-danger-main",
          gradientClass: "bg-gradient-end-3",
          chartColor: "#dc3545",
        };
      case "average response time":
        return {
          icon: "mdi:timer-outline",
          bgColor: "bg-warning-main",
          gradientClass: "bg-gradient-end-4",
          chartColor: "#f39c12",
        };
      default:
        return {
          icon: "mdi:chart-line",
          bgColor: "bg-info-main",
          gradientClass: "bg-gradient-end-5",
          chartColor: "#17a2b8",
        };
    }
  };

  return (
    <>
      {statisticsCards.map((card, index) => {
        const config = getCardConfig(card.title);

        return (
          <div key={index} className="col-xxl-3 col-lg-6 col-sm-6 mt-3">
            <div
              className={`card p-3 shadow-2 radius-8 border input-form-light h-100 ${config.gradientClass}`}
            >
              <div className="card-body p-0">
                <div className="d-flex flex-wrap align-items-center justify-content-between gap-1 mb-8">
                  <div className="d-flex align-items-center gap-2">
                    <span
                      className={`mb-0 w-48-px h-48-px ${config.bgColor} flex-shrink-0 text-white d-flex justify-content-center align-items-center rounded-circle h6 mb-0`}
                    >
                      <Icon icon={config.icon} className="icon" />
                    </span>
                    <div>
                      <span className="mb-2 fw-medium text-secondary-light text-sm">
                        {card.title}
                      </span>
                      <h6 className="fw-semibold">{card.value}</h6>
                    </div>
                  </div>
                  {/* Mini chart - sử dụng dữ liệu thực từ rawData */}
                  <div className="remove-tooltip-title rounded-tooltip-value">
                    {createMiniChart(config.chartColor, card.title, rawData)}
                  </div>
                </div>
                <p className="text-sm mb-0">{card.subtitle}</p>
              </div>
            </div>
          </div>
        );
      })}
    </>
  );
};

export default StatisticsCards;
