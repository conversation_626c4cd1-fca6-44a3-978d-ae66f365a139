import { useEffect, useState } from "react";
import $ from "jquery";
import "datatables.net-dt";
import { Icon } from "@iconify/react/dist/iconify.js";
import * as XLSX from "xlsx";

const ApiCallHistoryTable = ({ callHistoryData = [] }) => {
  // State for checkbox selections
  const [selectedRows, setSelectedRows] = useState(new Set());
  const [selectAll, setSelectAll] = useState(false);

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);

  // Temporarily disable DataTables for debugging
  /*
  useEffect(() => {
    
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable("#apiCallTable")) {
      $("#apiCallTable").DataTable().destroy();
    }

    // Only initialize DataTable if we have data
    if (callHistoryData && callHistoryData.length > 0) {
      const table = $("#apiCallTable").DataTable({
        pageLength: 10,
        order: [[4, "desc"]], // Sort by date descending
        columnDefs: [
          { orderable: false, targets: [0] }, // Disable sorting for checkbox column
          { orderable: false, targets: [7] }, // Disable sorting for action column
        ],
      });

      return () => {
        if ($.fn.DataTable.isDataTable("#apiCallTable")) {
          table.destroy(true);
        }
      };
    }
  }, [callHistoryData]);
  */

  // Helper function to get method badge
  const getMethodBadge = (method) => {
    const methodColors = {
      GET: "bg-info-focus text-info-main",
      POST: "bg-success-focus text-success-main",
      PUT: "bg-warning-focus text-warning-main",
      DELETE: "bg-danger-focus text-danger-main",
      PATCH: "bg-primary-focus text-primary-main",
    };

    const colorClass =
      methodColors[method] || "bg-neutral-200 text-neutral-600";

    return (
      <span
        className={`${colorClass} px-16 py-4 rounded-pill fw-medium text-xs d-inline-block text-center`}
        style={{ minWidth: "60px" }}
      >
        {method}
      </span>
    );
  };

  // Helper function to get status badge with fixed width
  const getStatusBadge = (statusCode, statusCategory) => {
    let badgeContent = "";
    let colorClass = "";

    if (statusCategory) {
      switch (statusCategory) {
        case "success":
          badgeContent = `${statusCode} Success`;
          colorClass = "bg-success-focus text-success-main";
          break;
        case "bad_request":
        case "conflict":
          badgeContent = `${statusCode} ${statusCategory.replace("_", " ")}`;
          colorClass = "bg-warning-focus text-warning-main";
          break;
        default:
          badgeContent = `${statusCode} ${statusCategory}`;
          colorClass = "bg-danger-focus text-danger-main";
          break;
      }
    } else {
      if (statusCode >= 200 && statusCode < 300) {
        badgeContent = `${statusCode} Success`;
        colorClass = "bg-success-focus text-success-main";
      } else if (statusCode >= 400 && statusCode < 500) {
        badgeContent = `${statusCode} Client Error`;
        colorClass = "bg-warning-focus text-warning-main";
      } else if (statusCode >= 500) {
        badgeContent = `${statusCode} Server Error`;
        colorClass = "bg-danger-focus text-danger-main";
      } else {
        badgeContent = statusCode;
        colorClass = "bg-neutral-200 text-neutral-600";
      }
    }

    return (
      <span
        className={`${colorClass} px-24 py-4 rounded-pill fw-medium text-sm d-inline-block text-center`}
        style={{ minWidth: "120px" }}
      >
        {badgeContent}
      </span>
    );
  };

  // Handle checkbox selection
  const handleRowSelect = (globalIndex) => {
    const newSelected = new Set(selectedRows);
    if (newSelected.has(globalIndex)) {
      newSelected.delete(globalIndex);
    } else {
      newSelected.add(globalIndex);
    }
    setSelectedRows(newSelected);

    // Check if all items on current page are selected
    const currentPageIndices = currentData.map(
      (_, index) => startIndex + index
    );
    const allCurrentPageSelected = currentPageIndices.every((index) =>
      newSelected.has(index)
    );
    setSelectAll(allCurrentPageSelected && currentPageIndices.length > 0);
  };

  // Handle select all
  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedRows(new Set());
    } else {
      // Only select items on current page
      const currentPageIndices = currentData.map(
        (_, index) => startIndex + index
      );
      setSelectedRows(new Set(currentPageIndices));
    }
    setSelectAll(!selectAll);
  };

  // Export to Excel function
  const exportToExcel = () => {
    const dataToExport =
      selectedRows.size > 0
        ? callHistoryData.filter((_, index) => selectedRows.has(index))
        : callHistoryData;

    const worksheet = XLSX.utils.json_to_sheet(
      dataToExport.map((call, index) => ({
        STT: index + 1,
        "Status Code": call.status_code,
        "Status Category": call.status_category,
        Method: call.method,
        Endpoint: call.endpoint,
        "Response Time": call.response_time,
        Date: formatDate(call.date),
        Partner: call.partner_username,
        "IP Address": call.ip_address,
      }))
    );

    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, "API Call History");
    XLSX.writeFile(
      workbook,
      `api-call-history-${new Date().toISOString().split("T")[0]}.xlsx`
    );
  };

  // Print function
  const handlePrint = () => {
    const dataToExport =
      selectedRows.size > 0
        ? callHistoryData.filter((_, index) => selectedRows.has(index))
        : callHistoryData;

    const printWindow = window.open("", "_blank");
    const printContent = `
      <html>
        <head>
          <title>API Call History</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; }
            table { width: 100%; border-collapse: collapse; margin-top: 20px; }
            th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
            th { background-color: #f2f2f2; font-weight: bold; }
            .status-success { background-color: #d4edda; color: #155724; padding: 4px 8px; border-radius: 12px; }
            .status-warning { background-color: #fff3cd; color: #856404; padding: 4px 8px; border-radius: 12px; }
            .status-danger { background-color: #f8d7da; color: #721c24; padding: 4px 8px; border-radius: 12px; }
          </style>
        </head>
        <body>
          <h1>API Call History Report</h1>
          <p>Generated on: ${new Date().toLocaleString()}</p>
          <p>Total records: ${dataToExport.length}</p>
          <table>
            <thead>
              <tr>
                <th>STT</th>
                <th>Status</th>
                <th>Method</th>
                <th>Endpoint</th>
                <th>Response Time</th>
                <th>Date & Time</th>
              </tr>
            </thead>
            <tbody>
              ${dataToExport
                .map(
                  (call, index) => `
                <tr>
                  <td>${index + 1}</td>
                  <td><span class="status-${
                    call.status_code >= 200 && call.status_code < 300
                      ? "success"
                      : call.status_code >= 400 && call.status_code < 500
                      ? "warning"
                      : "danger"
                  }">${call.status_code} ${
                    call.status_category || ""
                  }</span></td>
                  <td>${call.method}</td>
                  <td>${call.endpoint}</td>
                  <td>${call.response_time || "N/A"}</td>
                  <td>${formatDate(call.date)}</td>
                </tr>
              `
                )
                .join("")}
            </tbody>
          </table>
        </body>
      </html>
    `;

    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
  };

  // Use all data without filtering
  const filteredData = callHistoryData;

  // Pagination calculations
  const totalItems = filteredData.length;
  const totalPages = Math.ceil(totalItems / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Reset to first page (no filters dependency)
  useEffect(() => {
    setCurrentPage(1);
  }, []);

  // Reset selections when page changes
  useEffect(() => {
    setSelectedRows(new Set());
    setSelectAll(false);
  }, [currentPage, itemsPerPage]);

  // Handle page change
  const handlePageChange = (page) => {
    setCurrentPage(page);
  };

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(parseInt(e.target.value));
    setCurrentPage(1);
  };

  // Generate page numbers for pagination
  const getPageNumbers = () => {
    const pages = [];
    const maxVisiblePages = 5;
    let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
    let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

    if (endPage - startPage + 1 < maxVisiblePages) {
      startPage = Math.max(1, endPage - maxVisiblePages + 1);
    }

    for (let i = startPage; i <= endPage; i++) {
      pages.push(i);
    }
    return pages;
  };

  // Helper function to format date
  const formatDate = (dateString) => {
    if (!dateString) return "N/A";
    const date = new Date(dateString);
    return date.toLocaleString("vi-VN", {
      day: "2-digit",
      month: "2-digit",
      year: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Helper function to format response time
  const formatResponseTime = (time) => {
    if (!time && time !== 0) return "N/A";
    if (time < 1000) return `${time}ms`;
    return `${(time / 1000).toFixed(2)}s`;
  };

  return (
    <div className="card basic-data-table ">
      <div className="card-header">
        <div className="d-flex justify-content-between align-items-center">
          <div>
            <h5 className="card-title mb-0">API Call History</h5>
            <p className="text-sm text-secondary-light mb-0">
              Chi tiết lịch sử các API calls trong khoảng thời gian được chọn
            </p>
          </div>
          <div className="d-flex gap-2">
            <button
              type="button"
              className="btn btn-primary btn-sm"
              onClick={handlePrint}
              title="Print"
            >
              <Icon icon="mdi:printer" className="me-1" />
              Print
            </button>
            <button
              type="button"
              className="btn btn-success btn-sm"
              onClick={exportToExcel}
              title="Export to Excel"
            >
              <Icon icon="mdi:file-excel" className="me-1" />
              Excel
            </button>
          </div>
        </div>

        {/* Pagination Info */}
        <div className="row mt-3">
          <div className="col-md-12">
            <div className="d-flex align-items-center gap-2">
              <span className="text-sm text-secondary-light">
                Selected: {selectedRows.size} / {totalItems} records
              </span>
              <span className="text-sm text-secondary-light">|</span>
              <span className="text-sm text-secondary-light">
                Page {currentPage} of {totalPages}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div className="card-body">
        <table
          className="table bordered-table mb-0"
          id="apiCallTable"
          data-page-length={10}
        >
          <thead>
            <tr>
              <th scope="col" style={{ width: "60px" }}>
                <div className="form-check style-check d-flex align-items-center">
                  <input
                    className="form-check-input"
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                  />
                </div>
              </th>
              <th scope="col" style={{ width: "60px" }}>
                STT
              </th>
              <th scope="col">Status</th>
              <th scope="col">Method & Endpoint</th>
              <th scope="col">Response Time</th>
              <th scope="col">Date & Time</th>
            </tr>
          </thead>
          <tbody>
            {currentData && currentData.length > 0 ? (
              currentData.map((call, index) => {
                const globalIndex = startIndex + index;
                return (
                  <tr
                    key={call.request_trace || call.request_id || globalIndex}
                  >
                    <td>
                      <div className="form-check style-check d-flex align-items-center">
                        <input
                          className="form-check-input"
                          type="checkbox"
                          checked={selectedRows.has(globalIndex)}
                          onChange={() => handleRowSelect(globalIndex)}
                        />
                      </div>
                    </td>
                    <td>
                      <span className="text-sm fw-medium">
                        {String(globalIndex + 1).padStart(2, "0")}
                      </span>
                    </td>
                    <td>
                      {getStatusBadge(
                        call.status_code || 200,
                        call.status_category
                      )}
                    </td>
                    <td>
                      <div className="d-flex align-items-center gap-2">
                        {getMethodBadge(call.method || "GET")}
                        <span className="text-sm fw-medium">
                          {call.endpoint || "N/A"}
                        </span>
                      </div>
                    </td>
                    <td>
                      <span className="text-sm fw-medium">
                        {call.response_time || "N/A"}
                      </span>
                    </td>
                    <td>
                      <span className="text-sm">{formatDate(call.date)}</span>
                    </td>
                  </tr>
                );
              })
            ) : (
              <tr>
                <td colSpan="6" className="text-center py-4">
                  <div className="d-flex flex-column align-items-center gap-2">
                    <Icon
                      icon="mdi:database-off"
                      className="text-secondary-light"
                      style={{ fontSize: "48px" }}
                    />
                    <p className="text-secondary-light mb-0">
                      Không có dữ liệu API call history trong khoảng thời gian
                      được chọn
                    </p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>

        {/* Pagination Section */}
        {totalItems > 0 && (
          <div className="d-flex justify-content-between align-items-center mt-3">
            <div className="d-flex align-items-center gap-2">
              <span className="text-sm text-secondary-light">
                Items per page:
              </span>
              <select
                className="form-select form-select-sm"
                style={{ width: "auto" }}
                value={itemsPerPage}
                onChange={handleItemsPerPageChange}
              >
                <option value={5}>5</option>
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
              </select>
              <span className="text-sm text-secondary-light">
                Showing {startIndex + 1}-{Math.min(endIndex, totalItems)} of{" "}
                {totalItems} results
              </span>
            </div>

            <nav aria-label="Table pagination">
              <ul className="pagination pagination-sm mb-0">
                <li
                  className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
                >
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(1)}
                    disabled={currentPage === 1}
                  >
                    <Icon icon="lucide:chevrons-left" width="16" height="16" />
                  </button>
                </li>
                <li
                  className={`page-item ${currentPage === 1 ? "disabled" : ""}`}
                >
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(currentPage - 1)}
                    disabled={currentPage === 1}
                  >
                    <Icon icon="lucide:chevron-left" width="16" height="16" />
                  </button>
                </li>

                {getPageNumbers().map((page) => (
                  <li
                    key={page}
                    className={`page-item ${
                      currentPage === page ? "active" : ""
                    }`}
                  >
                    <button
                      className="page-link"
                      onClick={() => handlePageChange(page)}
                    >
                      {page}
                    </button>
                  </li>
                ))}

                <li
                  className={`page-item ${
                    currentPage === totalPages ? "disabled" : ""
                  }`}
                >
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(currentPage + 1)}
                    disabled={currentPage === totalPages}
                  >
                    <Icon icon="lucide:chevron-right" width="16" height="16" />
                  </button>
                </li>
                <li
                  className={`page-item ${
                    currentPage === totalPages ? "disabled" : ""
                  }`}
                >
                  <button
                    className="page-link"
                    onClick={() => handlePageChange(totalPages)}
                    disabled={currentPage === totalPages}
                  >
                    <Icon icon="lucide:chevrons-right" width="16" height="16" />
                  </button>
                </li>
              </ul>
            </nav>
          </div>
        )}
      </div>
    </div>
  );
};

export default ApiCallHistoryTable;
