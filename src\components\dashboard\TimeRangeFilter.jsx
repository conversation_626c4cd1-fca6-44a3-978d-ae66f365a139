import React, { useState } from "react";
import { Icon } from "@iconify/react/dist/iconify.js";

const TimeRangeFilter = ({
  timeRangeOptions,
  selectedTimeRange,
  setSelectedTimeRange,
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  const getDisplayTimeRange = () => {
    const option = timeRangeOptions.find((opt) => opt.id === selectedTimeRange);
    return option ? option.label : "Unknown";
  };
  return (
    <div className="col-12 mb-4">
      <div className="card radius-8 border-0">
        <div className="card-body p-24">
          <div className="d-flex align-items-center justify-content-left flex-wrap gap-3">
            <div className="d-flex align-items-center gap-2">
              <Icon
                icon={
                  isExpanded
                    ? "solar:alt-arrow-up-outline"
                    : "solar:alt-arrow-down-outline"
                }
                className="text-secondary-light cursor-pointer"
                style={{ fontSize: "18px" }}
                onClick={() => setIsExpanded(!isExpanded)}
              />
            </div>
            <div className="d-flex align-items-center gap-2">
              <Icon icon="solar:filter-outline" className="text-primary-600" />
              <span className="fw-medium text-secondary-light">Bộ lọc:</span>
              <span className="text-primary-600 fw-semibold">
                {getDisplayTimeRange()}
              </span>
            </div>
          </div>

          {isExpanded && (
            <div className="row mt-20">
              <div className="col-md-6">
                <div className="row g-3">
                  {timeRangeOptions.map((option) => (
                    <div key={option.id} className="col-6 col-sm-4">
                      <div className="form-check">
                        <input
                          className="form-check-input"
                          type="radio"
                          name="timeRange"
                          id={option.id}
                          checked={selectedTimeRange === option.id}
                          onChange={() => setSelectedTimeRange(option.id)}
                        />
                        <label
                          className="form-check-label text-sm"
                          htmlFor={option.id}
                        >
                          {option.label}
                        </label>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default TimeRangeFilter;
