// import useReactApexChart from "../../helper/useReactApexChart";
import ReactApexChart from "react-apexcharts";
import { Icon } from "@iconify/react/dist/iconify.js";

const ApiCallsChart = ({
  callsData,
  timeRange = "last12hours",
  groupBy = "hour",
}) => {
  // Dữ liệu từ API (theo json postman)
  const chartData = Array.isArray(callsData) ? callsData : [];

  // Kiểm tra nếu là time range ngắn hạn (hiển thị từng request)
  const shortTermRanges = [
    "last15minutes",
    "last30minutes",
    "last1hour",
    "last12hours",
  ];
  const isDetailedView = shortTermRanges.includes(timeRange);

  // Chuyển dữ liệu API sang format cho ApexCharts
  const categories = chartData.map((item) => item.time);

  let series;
  if (isDetailedView) {
    // Với time range ngắn hạn: hiển thị response time cho từng request
    series = [
      {
        name: "Response Time (ms)",
        data: chartData.map((item) => item.avg_response_time || 0),
      },
    ];
  } else {
    // Với time range dài hạn: hiển thị số lượng API calls
    series = [
      {
        name: "API Calls",
        data: chartData.map((item) => item.api_calls || 0),
      },
    ];
  }

  // Xác định format hiển thị dựa trên groupBy và detailed view
  const getTimeFormat = () => {
    if (isDetailedView) {
      return "detailed"; // Hiển thị chi tiết từng request
    }

    switch (groupBy) {
      case "minute":
        return "time"; // HH:mm
      case "hour":
        return "datetime"; // dd/mm HH:mm
      case "day":
      default:
        return "date"; // dd/mm
    }
  };

  // Chart options giống EarningStaticOne nhưng cho API Calls
  const options = {
    chart: {
      type: "bar",
      height: 310,
      toolbar: {
        show: false,
      },
    },
    plotOptions: {
      bar: {
        borderRadius: 4,
        horizontal: false,
        columnWidth:
          chartData.length <= 5
            ? "60%"
            : chartData.length <= 10
            ? "40%"
            : "23%", // Điều chỉnh width theo số lượng data
        endingShape: "rounded",
      },
    },
    dataLabels: {
      enabled: false,
    },
    fill: {
      type: "gradient",
      colors: [isDetailedView ? "#f39c12" : "#22c55e"], // Màu khác cho detailed view
      gradient: {
        shade: "light",
        type: "vertical",
        shadeIntensity: 0.5,
        gradientToColors: [isDetailedView ? "#f39c12" : "#22c55e"],
        inverseColors: false,
        opacityFrom: 1,
        opacityTo: 1,
        stops: [0, 100],
      },
    },
    grid: {
      show: true,
      borderColor: "#D1D5DB",
      strokeDashArray: 4,
      position: "back",
    },
    xaxis: {
      type: "category",
      categories,
      labels: {
        formatter: function (value) {
          const date = new Date(value);
          const timeFormat = getTimeFormat();

          switch (timeFormat) {
            case "detailed":
              // Với detailed view, hiển thị giờ:phút:giây
              return date.toLocaleTimeString("vi-VN", {
                hour: "2-digit",
                minute: "2-digit",
                second: "2-digit",
              });
            case "time":
              // Chỉ hiển thị giờ:phút cho minute grouping
              return date.toLocaleTimeString("vi-VN", {
                hour: "2-digit",
                minute: "2-digit",
              });
            case "datetime":
              // Với filter "Hôm nay", chỉ hiển thị giờ (HH:mm)
              return date.toLocaleTimeString("vi-VN", {
                hour: "2-digit",
                minute: "2-digit",
              });
            case "date":
            default:
              // Chỉ hiển thị ngày/tháng cho day grouping
              return date.toLocaleDateString("vi-VN", {
                day: "2-digit",
                month: "2-digit",
              });
          }
        },
        style: {
          fontSize: "12px",
        },
      },
    },
    yaxis: {
      labels: {
        style: {
          fontSize: "12px",
        },
      },
    },
    legend: {
      show: false,
    },
    tooltip: {
      x: {
        show: true,
        formatter: function (value) {
          const date = new Date(value);
          const timeFormat = getTimeFormat();

          switch (timeFormat) {
            case "detailed":
              // Hiển thị đầy đủ thông tin cho detailed view
              return date.toLocaleString("vi-VN");
            case "time":
              // Hiển thị giờ:phút:giây cho minute grouping
              return date.toLocaleTimeString("vi-VN");
            case "datetime":
              // Hiển thị đầy đủ ngày giờ cho hour grouping
              return date.toLocaleString("vi-VN");
            case "date":
            default:
              // Chỉ hiển thị ngày/tháng/năm cho day grouping
              return date.toLocaleDateString("vi-VN", {
                day: "2-digit",
                month: "2-digit",
                year: "numeric",
              });
          }
        },
      },
      y: {
        show: true,
        formatter: function (val, opts) {
          if (isDetailedView) {
            // Với detailed view, hiển thị thông tin chi tiết của request
            const dataIndex = opts.dataPointIndex;
            const requestData = chartData[dataIndex];

            return `
              <div>
                <div><strong>Response Time:</strong> ${val}ms</div>
                ${
                  requestData?.method
                    ? `<div><strong>Method:</strong> ${requestData.method}</div>`
                    : ""
                }
                ${
                  requestData?.endpoint
                    ? `<div><strong>Endpoint:</strong> ${requestData.endpoint}</div>`
                    : ""
                }
                ${
                  requestData?.status_code
                    ? `<div><strong>Status:</strong> ${requestData.status_code}</div>`
                    : ""
                }
              </div>
            `;
          } else {
            return `${val} API calls`;
          }
        },
      },
    },
  };

  return (
    <div className="col-12 col-md-6 col-lg-8 mt-3">
      <div className="card h-100 radius-8 border-0">
        <div className="card-body p-24">
          <div className="d-flex align-items-center flex-wrap gap-2 justify-content-between">
            <div>
              <h6 className="mb-2 fw-bold text-lg">
                {isDetailedView
                  ? "Chi tiết từng Request"
                  : "Thống kê API Calls"}
              </h6>
              <span className="text-sm fw-medium text-secondary-light">
                {isDetailedView
                  ? "Response time của từng request riêng biệt"
                  : "Tổng quan API calls theo thời gian"}
              </span>
            </div>
          </div>
          <div className="mt-20 d-flex justify-content-center flex-wrap gap-3">
            <div className="d-inline-flex align-items-center gap-2 p-2 radius-8 border pe-36 br-hover-primary group-item">
              <span className="bg-neutral-100 w-44-px h-44-px text-xxl radius-8 d-flex justify-content-center align-items-center  group-hover:bg-primary-600 group-hover:text-white">
                <Icon
                  icon={isDetailedView ? "mdi:timer-outline" : "mdi:api"}
                  className="icon"
                />
              </span>
              <div>
                <span className="text-secondary-light text-sm fw-medium">
                  {isDetailedView ? "Total Requests" : "API Calls"}
                </span>
                <h6 className="text-md fw-semibold mb-0">
                  {isDetailedView
                    ? chartData.length
                    : chartData.reduce(
                        (sum, item) => sum + (item.api_calls || 0),
                        0
                      )}
                </h6>
              </div>
            </div>
            <div className="d-inline-flex align-items-center gap-2 p-2 radius-8 border pe-36 br-hover-primary group-item">
              <span className="bg-neutral-100 w-44-px h-44-px text-xxl radius-8 d-flex justify-content-center align-items-center text-secondary-light group-hover:bg-primary-600 group-hover:text-white">
                <Icon icon="uis:chart" className="icon" />
              </span>
              <div>
                <span className="text-secondary-light text-sm fw-medium">
                  {isDetailedView ? "Avg Response" : "Errors"}
                </span>
                <h6 className="text-md fw-semibold mb-0">
                  {isDetailedView
                    ? `${Math.round(
                        chartData.reduce(
                          (sum, item) => sum + (item.avg_response_time || 0),
                          0
                        ) / chartData.length || 0
                      )}ms`
                    : chartData.reduce(
                        (sum, item) => sum + (item.errors || 0),
                        0
                      )}
                </h6>
              </div>
            </div>
            <div className="d-inline-flex align-items-center gap-2 p-2 radius-8 border pe-36 br-hover-primary group-item">
              <span className="bg-neutral-100 w-44-px h-44-px text-xxl radius-8 d-flex justify-content-center align-items-center text-secondary-light group-hover:bg-primary-600 group-hover:text-white">
                <Icon icon="ph:arrow-fat-up-fill" className="icon" />
              </span>
              <div>
                <span className="text-secondary-light text-sm fw-medium">
                  Avg Response Time
                </span>
                <h6 className="text-md fw-semibold mb-0">
                  {chartData.length > 0
                    ? Math.round(
                        chartData.reduce(
                          (sum, item) => sum + (item.avg_response_time || 0),
                          0
                        ) / chartData.length
                      )
                    : 0}{" "}
                  ms
                </h6>
              </div>
            </div>
          </div>
          <div id="barChart">
            <ReactApexChart
              options={options}
              series={series}
              type="bar"
              height={310}
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default ApiCallsChart;
