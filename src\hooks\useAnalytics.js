import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useAnalytics = () => {
  const { apiCall, loading, error } = useApi();
  const [analyticsData, setAnalyticsData] = useState(null);

  // Get analytics overview
  const getAnalyticsOverview = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.OVERVIEW}?${queryParams}` : API_ENDPOINTS.ANALYTICS.OVERVIEW;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        setAnalyticsData(response.data);
        return {
          success: true,
          data: response.data,
          message: response.message || 'Analytics overview loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load analytics overview');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load analytics overview'
      };
    }
  }, [apiCall]);

  // Get API calls over time
  const getCallsOverTime = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.CALLS_OVER_TIME}?${queryParams}` : API_ENDPOINTS.ANALYTICS.CALLS_OVER_TIME;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Calls over time data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load calls over time data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load calls over time data'
      };
    }
  }, [apiCall]);

  // Get top endpoints
  const getTopEndpoints = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.TOP_ENDPOINTS}?${queryParams}` : API_ENDPOINTS.ANALYTICS.TOP_ENDPOINTS;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Top endpoints data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load top endpoints data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load top endpoints data'
      };
    }
  }, [apiCall]);

  // Get status codes distribution
  const getStatusCodes = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.ANALYTICS.STATUS_CODES}?${queryParams}` : API_ENDPOINTS.ANALYTICS.STATUS_CODES;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success && response.data) {
        return {
          success: true,
          data: response.data,
          message: response.message || 'Status codes data loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load status codes data');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load status codes data'
      };
    }
  }, [apiCall]);

  // Get all call history (fetch all pages)
  const getAllCallHistory = useCallback(async (params = {}) => {
    let allData = [];
    let page = 1;
    let totalPages = 1;
    try {
      do {
        // Truyền limit = 1000 để lấy nhiều bản ghi hơn mặc định (25)
        const queryParams = new URLSearchParams({ ...params, page, limit: 1000 }).toString();
        const url = `${API_ENDPOINTS.ANALYTICS.CALL_HISTORY}?${queryParams}`;
        const response = await apiCall(url, { method: 'GET' });
        if (response.success && response.data) {
          allData = allData.concat(response.data);
          // Lấy thông tin phân trang nếu có
          if (response.pagination) {
            totalPages = response.pagination.total_pages || 1;
          } else {
            // Nếu không có phân trang, chỉ lấy 1 lần
            totalPages = 1;
          }
        } else {
          throw new Error(response.message || 'Failed to load call history');
        }
        page++;
      } while (page <= totalPages);
      return {
        success: true,
        data: allData,
        message: `All call history loaded successfully (${allData.length} records)`
      };
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load call history',
        data: []
      };
    }
  }, [apiCall]);

  return {
    analyticsData,
    getAnalyticsOverview,
    getCallsOverTime,
    getTopEndpoints,
    getStatusCodes,
    getCallHistory: getAllCallHistory,
    loading,
    error
  };
};

export default useAnalytics;
