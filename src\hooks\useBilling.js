import { useState, useCallback } from 'react';
import { useApi } from './useApi';
import { API_ENDPOINTS } from '../constants/api';

export const useBilling = () => {
  const { apiCall, loading, error } = useApi();
  const [bills, setBills] = useState([]);
  const [pagination, setPagination] = useState({});

  // Get list of bills
  const getBills = useCallback(async (params = {}) => {
    try {
      const queryParams = new URLSearchParams(params).toString();
      const url = queryParams ? `${API_ENDPOINTS.BILLING.LIST}?${queryParams}` : API_ENDPOINTS.BILLING.LIST;

      const response = await apiCall(url, {
        method: 'GET',
      });

      if (response.success) {
        const billsData = response.data || response.bills || [];
        setBills(billsData);
        setPagination(response.pagination || {});
        return {
          success: true,
          data: billsData,
          pagination: response.pagination,
          message: response.message || 'Bills loaded successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to load bills');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to load bills'
      };
    }
  }, [apiCall]);

  // Pay a bill
  const payBill = useCallback(async (billData) => {
    try {
      const response = await apiCall(API_ENDPOINTS.BILLING.PAY, {
        method: 'POST',
        data: billData,
      });

      if (response.success) {
        // Refresh bills list after payment
        await getBills();
        return {
          success: true,
          data: response.data,
          message: response.message || 'Bill paid successfully'
        };
      } else {
        throw new Error(response.message || 'Failed to pay bill');
      }
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to pay bill'
      };
    }
  }, [apiCall, getBills]);

  // Get bill statistics
  const getBillStats = useCallback(async () => {
    try {
      // This might be a custom endpoint or derived from bills list
      const response = await getBills();
      if (response.success) {
        const bills = response.data;
        const stats = {
          total: bills.length,
          paid: bills.filter(bill => bill.status === 'paid').length,
          unpaid: bills.filter(bill => bill.status === 'unpaid').length,
          overdue: bills.filter(bill => bill.status === 'overdue').length,
          totalAmount: bills.reduce((sum, bill) => sum + (bill.amount || 0), 0),
          unpaidAmount: bills.filter(bill => bill.status === 'unpaid').reduce((sum, bill) => sum + (bill.amount || 0), 0),
        };
        return {
          success: true,
          data: stats,
          message: 'Bill statistics calculated successfully'
        };
      }
      return response;
    } catch (err) {
      return {
        success: false,
        message: err.message || 'Failed to calculate bill statistics'
      };
    }
  }, [getBills]);

  return {
    bills,
    pagination,
    getBills,
    payBill,
    getBillStats,
    loading,
    error
  };
};

export default useBilling;
