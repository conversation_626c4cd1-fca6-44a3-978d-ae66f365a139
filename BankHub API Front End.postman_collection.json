{"info": {"_postman_id": "6b9af5d0-ebd6-4ded-98e7-04f5f7114ce6", "name": "BankHub API Front End", "description": "Complete API collection for BankHub system - Frontend Integration", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "********", "_collection_link": "https://web2mdev.postman.co/workspace/New-Team-Workspace~ad569215-229d-4a7d-9da3-5c326ae17281/collection/********-6b9af5d0-ebd6-4ded-98e7-04f5f7114ce6?action=share&source=collection_link&creator=********"}, "item": [{"name": "🔐 Authentication", "item": [{"name": "Partner Registration", "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON><PERSON>\",\n  \"last_name\": \"<PERSON>ng Mai\",\n  \"type\": \"individual\",\n  \"organization_name\": \"\",\n  \"email\": \"<EMAIL>\",\n  \"tax_or_id\": \"*********\",\n  \"address\": \"123 Main St, City\",\n  \"username\": \"lam\",\n  \"password\": \"StrongPass123!\",\n  \"plan_id\": 0\n}"}, "url": {"raw": "{{base_url}}/partner/register", "host": ["{{base_url}}"], "path": ["partner", "register"]}, "description": "Register new partner account"}, "response": []}, {"name": "Partner Login", "event": [{"listen": "test", "script": {"exec": ["if (pm.response.code === 200) {", "    const response = pm.response.json();", "    pm.globals.set('auth_token', response.token);", "    console.log('Auth token saved:', response.token);", "}"], "type": "text/javascript", "packages": {}}}], "request": {"method": "POST", "header": [{"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"username\": \"ng<PERSON><PERSON><PERSON>\",\n  \"password\": \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/partner/login", "host": ["{{base_url}}"], "path": ["partner", "login"]}, "description": "Login partner and get JWT token"}, "response": []}, {"name": "Get Current User Info", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/me", "host": ["{{base_url}}"], "path": ["me"]}, "description": "Get current authenticated partner information"}, "response": []}], "description": "Authentication endpoints for partner login/registration"}, {"name": "👤 Partner Management", "item": [{"name": "Get Partner Profile", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/partner/profile", "host": ["{{base_url}}"], "path": ["partner", "profile"]}, "description": "Get detailed partner profile information"}, "response": []}, {"name": "Update Partner Profile", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"first_name\": \"<PERSON>\",\n  \"last_name\": \"<PERSON><PERSON>\",\n  \"address\": \"456 New St, New City\"\n}"}, "url": {"raw": "{{base_url}}/partner/profile", "host": ["{{base_url}}"], "path": ["partner", "profile"]}, "description": "Update partner profile information"}, "response": []}, {"name": "Change Password", "request": {"method": "PUT", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"current_password\": \"StrongPass123!\",\n  \"new_password\": \"NewStrongPass123!\"\n}"}, "url": {"raw": "{{base_url}}/partner/password", "host": ["{{base_url}}"], "path": ["partner", "password"]}, "description": "Change partner password"}, "response": []}, {"name": "Forgot Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"email\": \"<EMAIL>\"\n}"}, "url": {"raw": "{{base_url}}/partner/forgot-password", "host": ["{{base_url}}"], "path": ["partner", "forgot-password"]}, "description": "Change partner password"}, "response": []}, {"name": "Reset Password", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"token\": \"token_from_email\",\n  \"new_password\" : \"NewPassword123!\"\n}"}, "url": {"raw": "{{base_url}}/partner/reset-password", "host": ["{{base_url}}"], "path": ["partner", "reset-password"]}, "description": "Change partner password"}, "response": []}, {"name": "Get Activity Logs", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/partner/activities?page=1&limit=20", "host": ["{{base_url}}"], "path": ["partner", "activities"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "20"}]}, "description": "Get partner activity logs with pagination"}, "response": []}, {"name": "Regenerate API Credentials", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"regenerate_type\" : \"api_secret\" // 'api_key', 'api_secret', or 'both'\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/partner/regenerate-credentials", "host": ["{{base_url}}"], "path": ["partner", "regenerate-credentials"]}, "description": "Generate new API key and secret for partner"}, "response": []}], "description": "Partner profile and account management"}, {"name": "📊 Dashboard APIs", "item": [{"name": "Dashboard Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/dashboard/overview", "host": ["{{base_url}}"], "path": ["dashboard", "overview"]}, "description": "Get dashboard overview with key metrics"}, "response": []}, {"name": "Usage Chart Data", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/dashboard/usage-chart?period=7", "host": ["{{base_url}}"], "path": ["dashboard", "usage-chart"], "query": [{"key": "period", "value": "7", "description": "Days to show (7, 30, 90)"}]}, "description": "Get usage chart data for dashboard"}, "response": []}, {"name": "Quick Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/dashboard/quick-stats", "host": ["{{base_url}}"], "path": ["dashboard", "quick-stats"]}, "description": "Get quick statistics for dashboard widgets"}, "response": []}], "description": "Dashboard data endpoints for frontend"}, {"name": "💳 Bank Account Management", "item": [{"name": "List Bank Accounts", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/bank-accounts?page=1&limit=10&status=active", "host": ["{{base_url}}"], "path": ["bank-accounts"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "active", "description": "active, inactive, all"}]}, "description": "Get list of bank accounts with pagination and filtering"}, "response": []}, {"name": "Get Bank Account Stats", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/bank-accounts/stats", "host": ["{{base_url}}"], "path": ["bank-accounts", "stats"]}, "description": "Get bank account statistics and summary"}, "response": []}, {"name": "Add Bank Account", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bank_name\": \"Vietcombank\",\n  \"account_number\": \"*********0\",\n  \"account_holder_name\": \"<PERSON>\"\n}"}, "url": {"raw": "{{base_url}}/bank-accounts/add", "host": ["{{base_url}}"], "path": ["bank-accounts", "add"]}, "description": "Add new bank account"}, "response": []}], "description": "Bank account management for frontend"}, {"name": "📋 Plan Management", "item": [{"name": "Get Available Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/plans/available", "host": ["{{base_url}}"], "path": ["plans", "available"]}, "description": "Get list of available subscription plans"}, "response": []}, {"name": "Get Available Plans Copy", "request": {"auth": {"type": "bearer", "bearer": [{"key": "token", "value": "a8f5f167f44f4964e6c998dee827110c", "type": "string"}]}, "method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "body": {"mode": "raw", "raw": "{\r\n    \"order_id\" : \"30\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{base_url}}/webhook/payment-success", "host": ["{{base_url}}"], "path": ["webhook", "payment-success"]}, "description": "Get list of available subscription plans"}, "response": []}, {"name": "Get Plan Usage", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/plans/usage", "host": ["{{base_url}}"], "path": ["plans", "usage"]}, "description": "Get current plan usage statistics"}, "response": []}, {"name": "Compare Plans", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/plans/compare", "host": ["{{base_url}}"], "path": ["plans", "compare"]}, "description": "Get plan comparison data"}, "response": []}, {"name": "Subscribe to Plan", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"plan_id\": 2,\n  \"payment_method\": \"bank_transfer\"\n}"}, "url": {"raw": "{{base_url}}/plans/subscribe", "host": ["{{base_url}}"], "path": ["plans", "subscribe"]}, "description": "Subscribe to a new plan"}, "response": []}, {"name": "Upgrade Plan", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"target_plan_id\": 2\n}"}, "url": {"raw": "{{base_url}}/plans/upgrade", "host": ["{{base_url}}"], "path": ["plans", "upgrade"]}, "description": "Subscribe to a new plan"}, "response": []}, {"name": "Get Subscription Status", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/plans/subscription-status", "host": ["{{base_url}}"], "path": ["plans", "subscription-status"]}, "description": "Get current subscription status"}, "response": []}], "description": "Plan subscription and management"}, {"name": "💰 Billing", "item": [{"name": "List Bills", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/bills?page=1&limit=10&status=unpaid", "host": ["{{base_url}}"], "path": ["bills"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "10"}, {"key": "status", "value": "unpaid", "description": "paid, unpaid, all"}]}, "description": "Get list of bills with pagination and filtering"}, "response": []}, {"name": "Pay Bill", "request": {"method": "POST", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}, {"key": "Content-Type", "value": "application/json"}], "body": {"mode": "raw", "raw": "{\n  \"bill_id\": 123,\n  \"payment_method\": \"bank_transfer\",\n  \"payment_reference\": \"TXN*********\"\n}"}, "url": {"raw": "{{base_url}}/bills/pay", "host": ["{{base_url}}"], "path": ["bills", "pay"]}, "description": "Pay a specific bill"}, "response": []}], "description": "Billing and payment management"}, {"name": "📈 Analytics", "item": [{"name": "API Overview", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/analytics/overview?timeRange=7", "host": ["{{base_url}}"], "path": ["analytics", "overview"], "query": [{"key": "timeRange", "value": "7", "description": "Days to analyze (1, 7, 30)"}]}, "description": "Get API usage overview and statistics"}, "response": []}, {"name": "API Calls Over Time", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/analytics/calls-over-time?timeRange=1&groupBy=minute", "host": ["{{base_url}}"], "path": ["analytics", "calls-over-time"], "query": [{"key": "timeRange", "value": "1"}, {"key": "groupBy", "value": "minute", "description": "minute, hour, day"}]}, "description": "Get API calls time series data for charts"}, "response": []}, {"name": "Top Endpoints", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/analytics/top-endpoints?timeRange=7&limit=10", "host": ["{{base_url}}"], "path": ["analytics", "top-endpoints"], "query": [{"key": "timeRange", "value": "7"}, {"key": "limit", "value": "10"}]}, "description": "Get most used API endpoints"}, "response": []}, {"name": "Status Code Distribution", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/analytics/status-codes?timeRange=7", "host": ["{{base_url}}"], "path": ["analytics", "status-codes"], "query": [{"key": "timeRange", "value": "7"}]}, "description": "Get status code distribution for pie charts"}, "response": []}, {"name": "API Call History", "request": {"method": "GET", "header": [{"key": "Authorization", "value": "Bearer {{auth_token}}"}], "url": {"raw": "{{base_url}}/analytics/call-history?page=1&limit=25&timeRange=7", "host": ["{{base_url}}"], "path": ["analytics", "call-history"], "query": [{"key": "page", "value": "1"}, {"key": "limit", "value": "25"}, {"key": "timeRange", "value": "7"}, {"key": "method", "value": "", "description": "Filter by HTTP method", "disabled": true}, {"key": "statusCode", "value": "", "description": "Filter by status code", "disabled": true}]}, "description": "Get detailed API call history with pagination"}, "response": []}], "description": "API analytics and monitoring data"}, {"name": "🔧 System", "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{base_url}}/health", "host": ["{{base_url}}"], "path": ["health"]}, "description": "Check API health status"}, "response": []}], "description": "System health and status endpoints"}, {"name": "ACB <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"masterMeta\": {\r\n        \"clientId\": \"3c3480b8-ab64-a525-a7ca-dde11313ea5a\",\r\n        \"clientRequestId\": \"a01a68ab-39c7-425c-99eb-2763ccfa1dd9\"\r\n    },\r\n    \"requests\": [\r\n        {\r\n            \"requestMeta\": {\r\n                \"requestType\": \"NOTIFICATION\",\r\n                \"requestCode\": \"TRANSACTION_UPDATE\"\r\n            },\r\n            \"requestParams\": {\r\n                \"transactions\": [\r\n                    {\r\n                        \"transactionStatus\": \"COMPLETED\",\r\n                        \"transactionChannel\": \"IBFT\",\r\n                        \"transactionCode\": \"563271234561223869\",\r\n                        \"accountNumber\": \"********\",\r\n                        \"transactionDate\": \"2022-09-19T03:28:51.000Z\",\r\n                        \"effectiveDate\": \"2022-09-18T17:00:00.000Z\",\r\n                        \"debitOrCredit\": \"credit\",\r\n                        \"virtualAccountInfo\": {\r\n                            \"vaPrefixCd\": \"HU1\",\r\n                            \"vaNbr\": null\r\n                        },\r\n                        \"amount\": 282801,\r\n                        \"transactionEntityAttribute\": {\r\n                            \"traceNumber\": null,\r\n                            \"beneficiaryName\": null,\r\n                            \"beneficiaryAccountNumber\": null,\r\n                            \"receiverBankName\": \"ACB\",\r\n                            \"remitterName\": null,\r\n                            \"remitterAccountNumber\": null,\r\n                            \"issuerBankName\": \"ACB\",\r\n                            \"virtualAccount\": \"HU1\",\r\n                            \"referenceNumber\": \"*********0\",\r\n                            \"partnerCustomerCode\": \"*********\",\r\n                            \"partnerCustomerName\": \"HA BAC NINH\",\r\n                            \"partnerCustomerType\": \"ORG\",\r\n                            \"custom1\": null,\r\n                            \"custom2\": null,\r\n                            \"custom3\": null,\r\n                            \"custom4\": null,\r\n                            \"custom5\": null,\r\n                            \"custom6\": null,\r\n                            \"custom7\": null,\r\n                            \"custom8\": null,\r\n                            \"custom9\": null,\r\n                            \"custom10\": null\r\n                        },\r\n                        \"transactionContent\": \"NAP1368\"\r\n                    }\r\n                ],\r\n                \"pagination\": {\r\n                    \"page\": 1,\r\n                    \"pageSize\": 100,\r\n                    \"totalPage\": 10\r\n                }\r\n            }\r\n        }\r\n    ]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/webhook/acb/transactions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "webhook", "acb", "transactions"]}}, "response": []}, {"name": "MB <PERSON>", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"transactionid\": \"ce5f1148-73cd-4863-9b4a-75940b4868fd\",\r\n    \"transactiontime\": *************,\r\n    \"referencenumber\": \"****************\",\r\n    \"amount\": 10000,\r\n    \"content\": \"********** L\",\r\n    \"bankaccount\": \"************\",\r\n    \"transType\": \"C\",\r\n    \"reciprocalAccount\": \"**********\",\r\n    \"reciprocalBankCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/webhook/mbbank/transactions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "webhook", "mbbank", "transactions"]}}, "response": []}, {"name": "VTB Hook Handle", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"transactionid\": \"ce5f1148-73cd-4863-9b4a-75940b4868fd\",\r\n    \"transactiontime\": *************,\r\n    \"referencenumber\": \"****************\",\r\n    \"amount\": 10000,\r\n    \"content\": \"********** L\",\r\n    \"bankaccount\": \"************\",\r\n    \"transType\": \"C\",\r\n    \"reciprocalAccount\": \"**********\",\r\n    \"reciprocalBankCode\": \"\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/webhook/mbbank/transactions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "webhook", "mbbank", "transactions"]}}, "response": []}, {"name": "BIDV Hook Handle", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"trans_id\": \"100004.200701.000016\",\r\n    \"trans_date\": \"**************\",\r\n    \"customer_id\": \"PAY2SS019\",\r\n    \"service_id\": \"W2M001\",\r\n    \"bill_id\": \"abc0003\",\r\n    \"amount\": \"120000\",\r\n    \"description\" : \"test\",\r\n    \"checksum\": \"bbb67b47601ae31cb6f611a516617d7c\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/webhook/bidv/transactions", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "webhook", "bidv", "transactions"]}}, "response": []}, {"name": "BIDV Hook Handle Copy", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"title\": \"Bảo trì định kỳ\",\r\n  \"description\": \"Bảo trì định kì...\",\r\n  \"severity\": \"low\",\r\n  \"affected_services\": [\"api\", \"webhook\", \"transactions\"]\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/incidents/ACB/create", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "incidents", "ACB", "create"]}}, "response": []}, {"name": "SYSTEM", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n  \"component_name\": \"api_gateway\",\r\n  \"title\": \"API Gateway Performance Issues\",\r\n  \"description\": \"High response times detected\",\r\n  \"severity\": \"medium\",\r\n  \"status\": \"investigating\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/system-status/incidents", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "system-status", "incidents"]}}, "response": []}, {"name": "BIDV Hook Handle Copy 2", "request": {"method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\r\n    \"resolution_description\": \"Issues fixed\",\r\n    \"new_status\": \"operational\"\r\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "http://localhost:3000/api/incidents/ACB/resolve", "protocol": "http", "host": ["localhost"], "port": "3000", "path": ["api", "incidents", "ACB", "resolve"]}}, "response": []}], "auth": {"type": "bearer", "bearer": [{"key": "token", "value": "{{auth_token}}", "type": "string"}]}, "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Auto-set base URL", "pm.globals.set('base_url', 'http://localhost:3000/api');"]}}, {"listen": "test", "script": {"type": "text/javascript", "packages": {}, "exec": [""]}}], "variable": [{"key": "base_url", "value": "https://api-partner.pay2s.vn", "type": "string"}, {"key": "auth_token", "value": "", "type": "string"}, {"key": "partner_id", "value": "", "type": "string"}]}