import React, { useEffect, useState } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react/dist/iconify.js";
import { usePlans } from "../hooks/usePlans";

const Plan = () => {
  const [plans, setPlans] = useState([]);
  const [error, setError] = useState(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [currentPlan, setCurrentPlan] = useState(null);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("bank_transfer");
  const [isProcessing, setIsProcessing] = useState(false);
  const {
    getAvailablePlans,
    getPlanUsage,
    subscribeToPlan,
    upgradePlan,
    loading,
  } = usePlans();

  useEffect(() => {
    const fetchPlans = async () => {
      setError(null);
      try {
        // Gọi cả 2 API: available plans và usage
        const [plansResult, usageResult] = await Promise.all([
          getAvailablePlans(),
          getPlanUsage(),
        ]);

        console.log("API result:", plansResult);
        console.log("Usage result:", usageResult);

        // Xử lý available plans
        if (
          plansResult.success &&
          plansResult.data &&
          Array.isArray(plansResult.data)
        ) {
          setPlans(plansResult.data);
        } else if (
          plansResult.success &&
          plansResult.plans &&
          Array.isArray(plansResult.plans)
        ) {
          setPlans(plansResult.plans);
        } else {
          setError(plansResult.message || "Không lấy được dữ liệu gói dịch vụ");
        }

        // Xử lý current plan
        if (usageResult.success && usageResult.current_plan) {
          setCurrentPlan(usageResult.current_plan);
        } else if (
          usageResult.success &&
          usageResult.data &&
          usageResult.data.current_plan
        ) {
          setCurrentPlan(usageResult.data.current_plan);
        }
      } catch (err) {
        setError("Lỗi khi gọi API: " + err.message);
      }
    };
    fetchPlans();
  }, [getAvailablePlans, getPlanUsage]);

  // Helper: lấy giá hiển thị cho period được chọn
  const getPriceForPeriod = (pricing, period) => {
    if (!pricing || !pricing[period]) return null;
    const item = pricing[period];
    return {
      price: Number(item.price).toLocaleString("vi-VN") + "₫",
      discount: item.discount ? `${item.discount}%` : null,
      savings: item.savings
        ? Number(item.savings).toLocaleString("vi-VN") + "₫"
        : null,
      duration: item.duration,
      rawPrice: item.price,
    };
  };

  // Helper: lấy label kỳ hạn
  const getPeriodLabel = (period) => {
    const labels = {
      monthly: "tháng",
      quarterly: "quý",
      semi_annual: "6 tháng",
      annual: "năm",
    };
    return labels[period] || period;
  };

  // Xử lý đăng ký/nâng cấp gói
  const handlePlanAction = (plan) => {
    const priceInfo = getPriceForPeriod(plan.pricing, selectedPeriod);
    if (!priceInfo) {
      alert("Gói này không khả dụng cho kỳ hạn đã chọn");
      return;
    }

    setSelectedPlan({ ...plan, priceInfo });
    setShowPaymentModal(true);
  };

  // Xử lý thanh toán
  const handlePayment = async () => {
    if (!selectedPlan) return;

    setIsProcessing(true);
    try {
      const paymentData = {
        plan_id: selectedPlan.id,
        payment_method: paymentMethod,
        period: selectedPeriod,
        amount: selectedPlan.priceInfo.rawPrice,
      };

      let result;
      const isUpgrade = currentPlan && currentPlan.id !== selectedPlan.id;

      if (isUpgrade) {
        result = await upgradePlan(selectedPlan.id, paymentData);
      } else {
        result = await subscribeToPlan(selectedPlan.id, paymentData);
      }

      if (result.success) {
        alert(
          result.message ||
            (isUpgrade ? "Nâng cấp gói thành công!" : "Đăng ký gói thành công!")
        );
        setShowPaymentModal(false);
        // Refresh data
        window.location.reload();
      } else {
        alert(result.message || "Có lỗi xảy ra khi xử lý thanh toán");
      }
    } catch (err) {
      alert("Lỗi: " + err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  return (
    <MasterLayout>
      <section className="row">
        <Breadcrumb title="Gói dịch vụ" />

        {/* Period Selection */}
        <div className="col-12">
          <div className="text-center">
            <div style={{ marginBottom: "40px" }}>
              <div className="d-flex align-items-center gap-3 justify-content-center flex-wrap">
                {[
                  { key: "monthly", label: "Hàng tháng" },
                  { key: "quarterly", label: "Hàng quý" },
                  { key: "semi_annual", label: "6 tháng" },
                  { key: "annual", label: "Hàng năm" },
                ].map((period) => (
                  <button
                    key={period.key}
                    className={`btn ${
                      selectedPeriod === period.key
                        ? "btn-primary"
                        : "btn btn-outline-secondary"
                    } btn-sm`}
                    onClick={() => setSelectedPeriod(period.key)}
                  >
                    {period.label}
                  </button>
                ))}
              </div>
            </div>
          </div>
        </div>

        <div className="col-12">
          <div className="card-body">
            <div className="row justify-content-center">
              <div className="col-xxl-10">
                <div className="row">
                  {loading ? (
                    <div className="text-center py-5">Đang tải bảng giá...</div>
                  ) : error ? (
                    <div className="alert alert-danger text-center">
                      {error}
                    </div>
                  ) : plans.length === 0 ? (
                    <div className="text-center py-5">
                      Không có gói dịch vụ khả dụng
                    </div>
                  ) : (
                    plans.map((plan) => {
                      const priceInfo = getPriceForPeriod(
                        plan.pricing,
                        selectedPeriod
                      );
                      const isCurrentPlan =
                        currentPlan && currentPlan.id === plan.id;

                      return (
                        <div className="col-xxl-6 col-md-6 mb-3" key={plan.id}>
                          <div
                            className={`pricing-plan position-relative radius-24 overflow-hidden border ${
                              isCurrentPlan
                                ? "featured-item bg-primary-600 text-white z-1"
                                : "bg-base"
                            }`}
                          >
                            <img
                              src="assets/images/pricing/pricing-shape.png"
                              alt="WowDash React Vite"
                              className="position-absolute end-0 top-10 z-n1"
                            />
                            {isCurrentPlan ? (
                              <span className="bg-white bg-opacity-25 text-white radius-24 py-8 px-24 text-sm position-absolute end-0 top-0 z-1 rounded-start-top-0 rounded-end-bottom-0">
                                Gói hiện tại
                              </span>
                            ) : null}
                            <div className="d-flex align-items-center gap-16">
                              <span className="w-72-px h-72-px d-flex justify-content-center align-items-center radius-16 bg-base">
                                <img
                                  src="assets/images/pricing/price-icon2.png"
                                  alt="WowDash React Vite"
                                />
                              </span>
                              <div className="">
                                <span
                                  className={`fw-medium text-md ${
                                    isCurrentPlan
                                      ? "text-white"
                                      : "text-secondary-light"
                                  }`}
                                >
                                  {plan.name}
                                </span>
                                <h6
                                  className={`mb-0 ${
                                    isCurrentPlan ? "text-white" : ""
                                  }`}
                                >
                                  {plan.name}
                                </h6>
                              </div>
                            </div>
                            <p
                              className={`mt-16 mb-0 ${
                                isCurrentPlan
                                  ? "text-white"
                                  : "text-secondary-light"
                              } mb-28`}
                            >
                              {plan.description}
                            </p>

                            {/* Price Display */}
                            {priceInfo ? (
                              <div className="mb-24">
                                <h6
                                  className={`mb-8 ${
                                    isCurrentPlan ? "text-white" : ""
                                  }`}
                                >
                                  {priceInfo.price}
                                  <span
                                    className={`fw-medium text-md ${
                                      isCurrentPlan
                                        ? "text-white"
                                        : "text-secondary-light"
                                    }`}
                                  >
                                    {" "}
                                    / {getPeriodLabel(selectedPeriod)}
                                  </span>
                                </h6>
                                {priceInfo.discount && (
                                  <div
                                    className={`small ${
                                      isCurrentPlan
                                        ? "text-white"
                                        : "text-success"
                                    }`}
                                  >
                                    🎉 Giảm {priceInfo.discount} - Tiết kiệm{" "}
                                    {priceInfo.savings}
                                  </div>
                                )}
                              </div>
                            ) : (
                              <div className="mb-24">
                                <h3
                                  className={`mb-8 ${
                                    isCurrentPlan
                                      ? "text-white"
                                      : "text-secondary-light"
                                  }`}
                                >
                                  Không có gói {getPeriodLabel(selectedPeriod)}
                                </h3>
                              </div>
                            )}

                            <span className="mb-20 fw-medium">
                              Giới hạn: {plan.account_limit} tài khoản
                            </span>
                            <ul>
                              {Array.isArray(plan.features) &&
                              plan.features.length > 0 ? (
                                plan.features.map((feature, idx) => (
                                  <li
                                    className="d-flex align-items-center gap-16 mb-16"
                                    key={idx}
                                  >
                                    <span className="w-24-px h-24-px d-flex justify-content-center align-items-center bg-white rounded-circle text-primary-600">
                                      <Icon
                                        icon="iconamoon:check-light"
                                        className="text-lg"
                                      />
                                    </span>
                                    <span
                                      className={
                                        isCurrentPlan
                                          ? "text-white text-lg"
                                          : "text-secondary-light text-lg"
                                      }
                                    >
                                      {feature}
                                    </span>
                                  </li>
                                ))
                              ) : (
                                <li className="d-flex align-items-center gap-16 mb-16">
                                  <span className="w-24-px h-24-px d-flex justify-content-center align-items-center bg-white rounded-circle text-primary-600">
                                    <Icon
                                      icon="iconamoon:check-light"
                                      className="text-lg"
                                    />
                                  </span>
                                  <span
                                    className={
                                      isCurrentPlan
                                        ? "text-white text-lg"
                                        : "text-secondary-light text-lg"
                                    }
                                  >
                                    Không có thông tin tính năng
                                  </span>
                                </li>
                              )}
                            </ul>
                            <button
                              className={`${
                                isCurrentPlan
                                  ? "bg-white text-primary-600 border-white"
                                  : priceInfo
                                  ? "bg-primary-600 text-white border-primary-600"
                                  : "bg-secondary text-white border-secondary"
                              } text-center text-sm btn-sm px-12 py-10 w-100 radius-8 mt-28`}
                              disabled={!priceInfo || loading}
                              onClick={() =>
                                priceInfo &&
                                !isCurrentPlan &&
                                handlePlanAction(plan)
                              }
                            >
                              {isCurrentPlan
                                ? "Gói hiện tại"
                                : priceInfo
                                ? currentPlan
                                  ? "Nâng cấp"
                                  : "Đăng ký ngay"
                                : "Không khả dụng"}
                            </button>
                          </div>
                        </div>
                      );
                    })
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Payment Modal */}
        {showPaymentModal && selectedPlan && (
          <div
            className="modal fade show d-block"
            style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
          >
            <div className="modal-dialog modal-lg">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">
                    {currentPlan
                      ? "Nâng cấp gói dịch vụ"
                      : "Đăng ký gói dịch vụ"}
                  </h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowPaymentModal(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6">
                      <h6>Thông tin gói</h6>
                      <div className="border rounded p-3 mb-3">
                        <h6 className="text-primary">{selectedPlan.name}</h6>
                        <p className="small text-muted mb-2">
                          {selectedPlan.description}
                        </p>
                        <div className="d-flex justify-content-between">
                          <span>Giá:</span>
                          <span className="fw-bold text-primary">
                            {selectedPlan.priceInfo.price} /{" "}
                            {getPeriodLabel(selectedPeriod)}
                          </span>
                        </div>
                        {selectedPlan.priceInfo.discount && (
                          <div className="d-flex justify-content-between text-success">
                            <span>Giảm giá:</span>
                            <span>{selectedPlan.priceInfo.discount}</span>
                          </div>
                        )}
                        <div className="d-flex justify-content-between">
                          <span>Giới hạn:</span>
                          <span>{selectedPlan.account_limit} tài khoản</span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <h6>Phương thức thanh toán</h6>
                      <div className="mb-3">
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="paymentMethod"
                            id="bankTransfer"
                            value="bank_transfer"
                            checked={paymentMethod === "bank_transfer"}
                            onChange={(e) => setPaymentMethod(e.target.value)}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="bankTransfer"
                          >
                            <Icon icon="mdi:bank-transfer" className="me-2" />
                            Chuyển khoản ngân hàng
                          </label>
                        </div>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="paymentMethod"
                            id="creditCard"
                            value="credit_card"
                            checked={paymentMethod === "credit_card"}
                            onChange={(e) => setPaymentMethod(e.target.value)}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="creditCard"
                          >
                            <Icon icon="mdi:credit-card" className="me-2" />
                            Thẻ tín dụng
                          </label>
                        </div>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="paymentMethod"
                            id="wallet"
                            value="e_wallet"
                            checked={paymentMethod === "e_wallet"}
                            onChange={(e) => setPaymentMethod(e.target.value)}
                          />
                          <label className="form-check-label" htmlFor="wallet">
                            <Icon icon="mdi:wallet" className="me-2" />
                            Ví điện tử
                          </label>
                        </div>
                      </div>

                      {paymentMethod === "bank_transfer" && (
                        <div className="alert alert-info">
                          <Icon icon="mdi:information" className="me-2" />
                          Sau khi xác nhận, bạn sẽ nhận được thông tin chuyển
                          khoản qua email.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowPaymentModal(false)}
                    disabled={isProcessing}
                  >
                    Hủy
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={handlePayment}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Đang xử lý...
                      </>
                    ) : (
                      <>Xác nhận thanh toán</>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>
    </MasterLayout>
  );
};

export default Plan;
