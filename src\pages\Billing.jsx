import React, { useEffect, useState } from "react";
import MasterLayout from "../masterLayout/MasterLayout";
import Breadcrumb from "../components/Breadcrumb";
import { Icon } from "@iconify/react/dist/iconify.js";
import { useBilling } from "../hooks/useBilling";

const Billing = () => {
  const [bills, setBills] = useState([]);
  const [stats, setStats] = useState({});
  const [error, setError] = useState(null);
  const [filter, setFilter] = useState("all");
  const [currentPage, setCurrentPage] = useState(1);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [selectedBill, setSelectedBill] = useState(null);
  const [paymentMethod, setPaymentMethod] = useState("bank_transfer");
  const [paymentReference, setPaymentReference] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);

  const { getBills, payBill, getBillStats, loading } = useBilling();

  useEffect(() => {
    const fetchData = async () => {
      setError(null);
      try {
        const [billsResult, statsResult] = await Promise.all([
          getBills({
            page: currentPage,
            limit: 10,
            status: filter !== "all" ? filter : undefined,
          }),
          getBillStats(),
        ]);

        if (billsResult.success) {
          setBills(billsResult.data || []);
        } else {
          setError(billsResult.message || "Không lấy được dữ liệu hóa đơn");
        }

        if (statsResult.success) {
          setStats(statsResult.data || {});
        }
      } catch (err) {
        setError("Lỗi khi gọi API: " + err.message);
      }
    };
    fetchData();
  }, [getBills, getBillStats, currentPage, filter]);

  // Format currency
  const formatCurrency = (amount) => {
    return Number(amount || 0).toLocaleString("vi-VN") + "₫";
  };

  // Format date
  const formatDate = (dateString) => {
    if (!dateString) return "";
    return new Date(dateString).toLocaleDateString("vi-VN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
    });
  };

  // Get status badge
  const getStatusBadge = (status) => {
    const statusConfig = {
      paid: {
        class: "bg-success",
        text: "Đã thanh toán",
        icon: "mdi:check-circle",
      },
      unpaid: {
        class: "bg-warning",
        text: "Chưa thanh toán",
        icon: "mdi:clock",
      },
      overdue: {
        class: "bg-danger",
        text: "Quá hạn",
        icon: "mdi:alert-circle",
      },
      processing: {
        class: "bg-info",
        text: "Đang xử lý",
        icon: "mdi:progress-clock",
      },
    };

    const config = statusConfig[status] || statusConfig.unpaid;
    return (
      <span className={`badge ${config.class}`}>
        <Icon icon={config.icon} className="me-1" />
        {config.text}
      </span>
    );
  };

  // Handle payment
  const handlePayment = async () => {
    if (!selectedBill) return;

    setIsProcessing(true);
    try {
      const paymentData = {
        bill_id: selectedBill.id,
        payment_method: paymentMethod,
        payment_reference: paymentReference,
      };

      const result = await payBill(paymentData);

      if (result.success) {
        alert(result.message || "Thanh toán thành công!");
        setShowPaymentModal(false);
        setPaymentReference("");
        // Refresh data
        window.location.reload();
      } else {
        alert(result.message || "Có lỗi xảy ra khi thanh toán");
      }
    } catch (err) {
      alert("Lỗi: " + err.message);
    } finally {
      setIsProcessing(false);
    }
  };

  // Handle payment modal
  const openPaymentModal = (bill) => {
    setSelectedBill(bill);
    setShowPaymentModal(true);
  };

  const filteredBills = bills.filter((bill) => {
    if (filter === "all") return true;
    return bill.status === filter;
  });

  return (
    <MasterLayout>
      <section className="row">
        <Breadcrumb title="Hóa đơn & Thanh toán" />

        {/* Statistics Cards */}
        <div className="col-12 mb-4">
          <div className="row">
            <div className="col-lg-3 col-md-6 mb-3">
              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="w-40-px h-40-px d-flex justify-content-center align-items-center bg-primary bg-opacity-10 rounded me-3">
                      <Icon
                        icon="mdi:file-document"
                        className="text-primary fs-5"
                      />
                    </div>
                    <div>
                      <h6 className="mb-0">{stats.total || 0}</h6>
                      <small className="text-muted">Tổng hóa đơn</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-3">
              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="w-40-px h-40-px d-flex justify-content-center align-items-center bg-success bg-opacity-10 rounded me-3">
                      <Icon
                        icon="mdi:check-circle"
                        className="text-success fs-5"
                      />
                    </div>
                    <div>
                      <h6 className="mb-0">{stats.paid || 0}</h6>
                      <small className="text-muted">Đã thanh toán</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-3">
              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="w-40-px h-40-px d-flex justify-content-center align-items-center bg-warning bg-opacity-10 rounded me-3">
                      <Icon icon="mdi:clock" className="text-warning fs-5" />
                    </div>
                    <div>
                      <h6 className="mb-0">{stats.unpaid || 0}</h6>
                      <small className="text-muted">Chưa thanh toán</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div className="col-lg-3 col-md-6 mb-3">
              <div className="card border-0 shadow-sm">
                <div className="card-body">
                  <div className="d-flex align-items-center">
                    <div className="w-40-px h-40-px d-flex justify-content-center align-items-center bg-info bg-opacity-10 rounded me-3">
                      <Icon
                        icon="mdi:currency-usd"
                        className="text-info fs-5"
                      />
                    </div>
                    <div>
                      <h6 className="mb-0">
                        {formatCurrency(stats.unpaidAmount)}
                      </h6>
                      <small className="text-muted">Tổng phải thanh toán</small>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Filters and Bills Table */}
        <div className="col-12">
          <div className="card border-0 shadow-sm">
            <div className="card-header bg-white border-bottom">
              <div className="d-flex justify-content-between align-items-center">
                <h5 className="mb-0">Danh sách hóa đơn</h5>
                <div className="d-flex gap-2">
                  {[
                    { key: "all", label: "Tất cả" },
                    { key: "unpaid", label: "Chưa thanh toán" },
                    { key: "paid", label: "Đã thanh toán" },
                    { key: "overdue", label: "Quá hạn" },
                  ].map((filterOption) => (
                    <button
                      key={filterOption.key}
                      className={`btn btn-sm ${
                        filter === filterOption.key
                          ? "btn-primary"
                          : "btn-outline-primary"
                      }`}
                      onClick={() => setFilter(filterOption.key)}
                    >
                      {filterOption.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
            <div className="card-body">
              {loading ? (
                <div className="text-center py-5">
                  <div className="spinner-border text-primary"></div>
                  <div className="mt-2">Đang tải dữ liệu...</div>
                </div>
              ) : error ? (
                <div className="alert alert-danger text-center">
                  <Icon icon="mdi:alert-circle" className="me-2" />
                  {error}
                </div>
              ) : filteredBills.length === 0 ? (
                <div className="text-center py-5">
                  <Icon
                    icon="mdi:file-document-outline"
                    className="fs-1 text-muted mb-3"
                  />
                  <div className="text-muted">Không có hóa đơn nào</div>
                </div>
              ) : (
                <div className="table-responsive">
                  <table className="table table-hover">
                    <thead className="table-light">
                      <tr>
                        <th>ID</th>
                        <th>Ngày tạo</th>
                        <th>Mô tả</th>
                        <th>Số tiền</th>
                        <th>Hạn thanh toán</th>
                        <th>Trạng thái</th>
                        <th>Thao tác</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredBills.map((bill) => (
                        <tr key={bill.id}>
                          <td>
                            <span className="fw-medium">#{bill.id}</span>
                          </td>
                          <td>{formatDate(bill.created_at)}</td>
                          <td>
                            <div>
                              <div className="fw-medium">
                                {bill.description || "Thanh toán dịch vụ"}
                              </div>
                              {bill.plan_name && (
                                <small className="text-muted">
                                  Gói: {bill.plan_name}
                                </small>
                              )}
                            </div>
                          </td>
                          <td>
                            <span className="fw-bold text-primary">
                              {formatCurrency(bill.amount)}
                            </span>
                          </td>
                          <td>{formatDate(bill.due_date)}</td>
                          <td>{getStatusBadge(bill.status)}</td>
                          <td>
                            <div className="d-flex gap-1">
                              {bill.status === "unpaid" && (
                                <button
                                  className="btn btn-sm btn-primary"
                                  onClick={() => openPaymentModal(bill)}
                                  title="Thanh toán"
                                >
                                  <Icon icon="mdi:credit-card" />
                                </button>
                              )}
                              <button
                                className="btn btn-sm btn-outline-secondary"
                                title="Xem chi tiết"
                              >
                                <Icon icon="mdi:eye" />
                              </button>
                              <button
                                className="btn btn-sm btn-outline-secondary"
                                title="Tải về"
                              >
                                <Icon icon="mdi:download" />
                              </button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Payment Modal */}
        {showPaymentModal && selectedBill && (
          <div
            className="modal fade show d-block"
            style={{ backgroundColor: "rgba(0,0,0,0.5)" }}
          >
            <div className="modal-dialog modal-lg">
              <div className="modal-content">
                <div className="modal-header">
                  <h5 className="modal-title">Thanh toán hóa đơn</h5>
                  <button
                    type="button"
                    className="btn-close"
                    onClick={() => setShowPaymentModal(false)}
                  ></button>
                </div>
                <div className="modal-body">
                  <div className="row">
                    <div className="col-md-6">
                      <h6>Thông tin hóa đơn</h6>
                      <div className="border rounded p-3 mb-3">
                        <div className="d-flex justify-content-between mb-2">
                          <span>Mã hóa đơn:</span>
                          <span className="fw-bold">#{selectedBill.id}</span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Mô tả:</span>
                          <span>
                            {selectedBill.description || "Thanh toán dịch vụ"}
                          </span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Số tiền:</span>
                          <span className="fw-bold text-primary">
                            {formatCurrency(selectedBill.amount)}
                          </span>
                        </div>
                        <div className="d-flex justify-content-between mb-2">
                          <span>Hạn thanh toán:</span>
                          <span>{formatDate(selectedBill.due_date)}</span>
                        </div>
                        <div className="d-flex justify-content-between">
                          <span>Trạng thái:</span>
                          <span>{getStatusBadge(selectedBill.status)}</span>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6">
                      <h6>Phương thức thanh toán</h6>
                      <div className="mb-3">
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="paymentMethod"
                            id="bankTransfer"
                            value="bank_transfer"
                            checked={paymentMethod === "bank_transfer"}
                            onChange={(e) => setPaymentMethod(e.target.value)}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="bankTransfer"
                          >
                            <Icon icon="mdi:bank-transfer" className="me-2" />
                            Chuyển khoản ngân hàng
                          </label>
                        </div>
                        <div className="form-check">
                          <input
                            className="form-check-input"
                            type="radio"
                            name="paymentMethod"
                            id="creditCard"
                            value="credit_card"
                            checked={paymentMethod === "credit_card"}
                            onChange={(e) => setPaymentMethod(e.target.value)}
                          />
                          <label
                            className="form-check-label"
                            htmlFor="creditCard"
                          >
                            <Icon icon="mdi:credit-card" className="me-2" />
                            Thẻ tín dụng
                          </label>
                        </div>
                      </div>

                      <div className="mb-3">
                        <label className="form-label">
                          Mã tham chiếu thanh toán
                        </label>
                        <input
                          type="text"
                          className="form-control"
                          placeholder="Nhập mã giao dịch (nếu có)"
                          value={paymentReference}
                          onChange={(e) => setPaymentReference(e.target.value)}
                        />
                        <small className="text-muted">
                          Mã này giúp hệ thống xác minh thanh toán của bạn
                        </small>
                      </div>

                      {paymentMethod === "bank_transfer" && (
                        <div className="alert alert-info">
                          <Icon icon="mdi:information" className="me-2" />
                          Vui lòng chuyển khoản theo thông tin được cung cấp và
                          nhập mã giao dịch vào ô trên.
                        </div>
                      )}
                    </div>
                  </div>
                </div>
                <div className="modal-footer">
                  <button
                    type="button"
                    className="btn btn-secondary"
                    onClick={() => setShowPaymentModal(false)}
                    disabled={isProcessing}
                  >
                    Hủy
                  </button>
                  <button
                    type="button"
                    className="btn btn-primary"
                    onClick={handlePayment}
                    disabled={isProcessing}
                  >
                    {isProcessing ? (
                      <>
                        <span className="spinner-border spinner-border-sm me-2"></span>
                        Đang xử lý...
                      </>
                    ) : (
                      <>Xác nhận thanh toán</>
                    )}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </section>
    </MasterLayout>
  );
};

export default Billing;
